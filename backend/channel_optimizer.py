"""
Channel Optimizer Back<PERSON> Script
Aggregates all channel optimization functionality including channel selection logic,
multi-channel campaign orchestration, and delivery optimization.

This script combines functionality from:
- src/openengage/core/channel_selector.py
- src/openengage/core/channel_selection_util.py
- src/openengage/ui/channel_selection.py
- Related utility functions

Inputs:
- user_data: Dict or List[Dict] containing user data with email/phone
- content_type: Type of content being sent ("general", "promotional", "transactional")
- urgency_level: Level of urgency ("low", "medium", "high")
- performance_data_path: Optional path to performance data CSV
- weights: Optional dict of weights for different factors
- engagement_data: Optional pre-computed engagement metrics

Outputs:
- selected_channel: Optimal channel for user ("email" or "whatsapp")
- channel_scores: Dict with scores for each channel
- channel_groups: Dict with users grouped by selected channel
- optimization_results: Dict with campaign optimization results
"""

import os
import logging
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

class ChannelOptimizer:
    """Optimizer for selecting optimal marketing channels for users"""

    # Channel types
    EMAIL = "email"
    WHATSAPP = "whatsapp"
    
    # Default weights for different factors in channel selection
    DEFAULT_WEIGHTS = {
        "availability": 0.4,        # Weight for channel availability
        "engagement": 0.3,          # Weight for past engagement
        "content_suitability": 0.2, # Weight for content suitability
        "urgency": 0.1,             # Weight for message urgency
    }

    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        Initialize the channel optimizer.
        
        Args:
            weights: Optional dictionary of weights for different factors
        """
        self.weights = weights or self.DEFAULT_WEIGHTS.copy()
        self.logger = self._setup_logger()
        
        # Validate weights
        if abs(sum(self.weights.values()) - 1.0) > 0.01:
            self.logger.warning("Weights do not sum to 1.0, normalizing...")
            total = sum(self.weights.values())
            self.weights = {k: v/total for k, v in self.weights.items()}

    def _setup_logger(self):
        """Set up logger for the optimizer"""
        logger = logging.getLogger("openengage.channel_optimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def get_channel_availability(self, user_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Determine channel availability for the user.
        
        Args:
            user_data: Dictionary containing user data
        
        Returns:
            Dictionary with availability score for each channel (0-1)
        """
        availability = {
            self.EMAIL: 0.0,
            self.WHATSAPP: 0.0,
        }
        
        # Email is available if user_email exists and is valid
        if user_data.get("user_email") and isinstance(user_data["user_email"], str) and "@" in user_data["user_email"]:
            availability[self.EMAIL] = 1.0
            
        # WhatsApp is available if phone_number exists and is valid
        if user_data.get("phone_number") and isinstance(user_data["phone_number"], str) and len(user_data["phone_number"]) >= 10:
            availability[self.WHATSAPP] = 1.0
            
        return availability

    def get_engagement_scores(self, user_data: Dict[str, Any], 
                             engagement_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        Calculate engagement scores for each channel based on historical data.
        
        Args:
            user_data: Dictionary containing user data
            engagement_data: Optional pre-computed engagement metrics
        
        Returns:
            Dictionary with engagement score for each channel (0-1)
        """
        engagement = {
            self.EMAIL: 0.5,      # Default neutral score
            self.WHATSAPP: 0.5,   # Default neutral score
        }
        
        user_email = user_data.get("user_email", "")
        
        # Use provided engagement data if available
        if engagement_data and user_email in engagement_data:
            user_engagement = engagement_data[user_email]
            engagement[self.EMAIL] = user_engagement.get("email_engagement", 0.5)
            engagement[self.WHATSAPP] = user_engagement.get("whatsapp_engagement", 0.5)
        else:
            # Calculate engagement based on user behavior data
            user_behavior = user_data.get("user_behaviour", "").lower()
            
            # Email engagement heuristics
            if any(keyword in user_behavior for keyword in ["email", "newsletter", "subscribed"]):
                engagement[self.EMAIL] = 0.8
            elif any(keyword in user_behavior for keyword in ["unsubscribed", "spam", "blocked"]):
                engagement[self.EMAIL] = 0.1
            
            # WhatsApp engagement heuristics
            if any(keyword in user_behavior for keyword in ["whatsapp", "mobile", "instant"]):
                engagement[self.WHATSAPP] = 0.8
            elif any(keyword in user_behavior for keyword in ["no mobile", "landline"]):
                engagement[self.WHATSAPP] = 0.2
                
        return engagement

    def get_content_suitability(self, content_type: str) -> Dict[str, float]:
        """
        Determine content suitability for each channel based on content type.
        
        Args:
            content_type: Type of content being sent
        
        Returns:
            Dictionary with suitability score for each channel (0-1)
        """
        suitability_matrix = {
            "general": {self.EMAIL: 0.7, self.WHATSAPP: 0.6},
            "promotional": {self.EMAIL: 0.8, self.WHATSAPP: 0.5},
            "transactional": {self.EMAIL: 0.9, self.WHATSAPP: 0.8},
            "urgent": {self.EMAIL: 0.4, self.WHATSAPP: 0.9},
            "newsletter": {self.EMAIL: 0.9, self.WHATSAPP: 0.3},
            "reminder": {self.EMAIL: 0.6, self.WHATSAPP: 0.8},
            "welcome": {self.EMAIL: 0.8, self.WHATSAPP: 0.6},
            "follow_up": {self.EMAIL: 0.7, self.WHATSAPP: 0.7}
        }
        
        return suitability_matrix.get(content_type.lower(), {self.EMAIL: 0.5, self.WHATSAPP: 0.5})

    def get_urgency_scores(self, urgency_level: str) -> Dict[str, float]:
        """
        Calculate urgency scores for each channel.
        
        Args:
            urgency_level: Level of urgency ("low", "medium", "high")
        
        Returns:
            Dictionary with urgency score for each channel (0-1)
        """
        urgency_matrix = {
            "low": {self.EMAIL: 0.8, self.WHATSAPP: 0.4},
            "medium": {self.EMAIL: 0.6, self.WHATSAPP: 0.7},
            "high": {self.EMAIL: 0.3, self.WHATSAPP: 0.9}
        }
        
        return urgency_matrix.get(urgency_level.lower(), {self.EMAIL: 0.5, self.WHATSAPP: 0.5})

    def _calculate_engagement_from_performance_data(self, performance_data_path: str) -> Dict[str, Dict[str, float]]:
        """
        Calculate engagement metrics from performance data file.
        
        Args:
            performance_data_path: Path to performance data CSV
        
        Returns:
            Dictionary mapping user emails to engagement scores
        """
        engagement_data = {}
        
        try:
            if os.path.exists(performance_data_path):
                df = pd.read_csv(performance_data_path)
                
                # Calculate email engagement (open rate and click rate)
                if 'user_email' in df.columns:
                    email_metrics = df.groupby('user_email').agg({
                        'Open_Time': lambda x: x.notna().sum() / len(x),  # Open rate
                        'Click_Time': lambda x: x.notna().sum() / len(x)  # Click rate
                    }).fillna(0)
                    
                    for email, metrics in email_metrics.iterrows():
                        open_rate = metrics.get('Open_Time', 0)
                        click_rate = metrics.get('Click_Time', 0)
                        
                        # Calculate combined engagement score
                        email_engagement = (open_rate * 0.7 + click_rate * 0.3)
                        
                        engagement_data[email] = {
                            "email_engagement": min(email_engagement, 1.0),
                            "whatsapp_engagement": 0.5  # Default for WhatsApp
                        }
                        
        except Exception as e:
            self.logger.warning(f"Error calculating engagement from performance data: {str(e)}")
            
        return engagement_data

    def select_channel(self, user_data: Dict[str, Any], 
                       content_type: str = "general", 
                       urgency_level: str = "medium",
                       engagement_data: Optional[Dict[str, Any]] = None) -> Tuple[str, Dict[str, float]]:
        """
        Select the optimal channel for the user based on various factors.
        
        Args:
            user_data: Dictionary containing user data (must include user_email and/or phone_number)
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")
            engagement_data: Optional dictionary containing pre-computed engagement metrics
        
        Returns:
            Tuple of (selected_channel, scores_dict)
        """
        # Calculate scores for each factor
        availability = self.get_channel_availability(user_data)
        engagement = self.get_engagement_scores(user_data, engagement_data)
        content_suitability = self.get_content_suitability(content_type)
        urgency = self.get_urgency_scores(urgency_level)
        
        # Calculate weighted scores for each channel
        scores = {}
        for channel in [self.EMAIL, self.WHATSAPP]:
            # If channel is not available, score is 0
            if availability[channel] == 0:
                scores[channel] = 0
                continue
                
            # Calculate weighted score
            score = (
                self.weights["availability"] * availability[channel] +
                self.weights["engagement"] * engagement[channel] +
                self.weights["content_suitability"] * content_suitability[channel] +
                self.weights["urgency"] * urgency[channel]
            )
            scores[channel] = score
        
        # Get the channel with the highest score
        if scores[self.EMAIL] >= scores[self.WHATSAPP]:
            selected_channel = self.EMAIL
        else:
            selected_channel = self.WHATSAPP
            
        # If selected channel has no availability, use the alternative channel if available
        if availability[selected_channel] == 0:
            alternative = self.WHATSAPP if selected_channel == self.EMAIL else self.EMAIL
            if availability[alternative] > 0:
                selected_channel = alternative
            else:
                # If neither channel is available, default to email (for logging)
                selected_channel = self.EMAIL
                self.logger.warning(f"No valid channel found for user: {user_data.get('user_email', 'unknown')}")
        
        # Return selected channel and scores
        return selected_channel, scores

    def batch_select_channels(self, users_data: List[Dict[str, Any]],
                             content_type: str = "general",
                             urgency_level: str = "medium",
                             engagement_data: Optional[Dict[str, Any]] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Select channels for a batch of users and group them by selected channel.

        Args:
            users_data: List of dictionaries containing user data
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")
            engagement_data: Optional pre-computed engagement metrics

        Returns:
            Dictionary with users grouped by selected channel
        """
        # Group users by channel
        channels = {
            self.EMAIL: [],
            self.WHATSAPP: [],
            "unavailable": []
        }

        # Process each user
        for user_data in users_data:
            # Skip if neither email nor phone number exists
            if not user_data.get("user_email") and not user_data.get("phone_number"):
                channels["unavailable"].append(user_data)
                continue

            # Select channel for user
            selected_channel, scores = self.select_channel(
                user_data,
                content_type=content_type,
                urgency_level=urgency_level,
                engagement_data=engagement_data
            )

            # Add channel selection info to user data
            user_data_with_channel = user_data.copy()
            user_data_with_channel["selected_channel"] = selected_channel
            user_data_with_channel["email_score"] = scores.get(self.EMAIL, 0)
            user_data_with_channel["whatsapp_score"] = scores.get(self.WHATSAPP, 0)

            # Add user to the appropriate channel group
            if selected_channel in channels:
                channels[selected_channel].append(user_data_with_channel)

        return channels

    def optimize_campaign_timing(self, users_data: List[Dict[str, Any]],
                                content_type: str = "general") -> Dict[str, Any]:
        """
        Optimize campaign timing based on user behavior and channel characteristics.

        Args:
            users_data: List of user data dictionaries
            content_type: Type of content being sent

        Returns:
            Dictionary with timing optimization results
        """
        timing_recommendations = {
            "email": {
                "best_days": ["Tuesday", "Wednesday", "Thursday"],
                "best_hours": [9, 10, 14, 15],  # 9-10 AM, 2-3 PM
                "avoid_hours": [0, 1, 2, 3, 4, 5, 22, 23]  # Late night/early morning
            },
            "whatsapp": {
                "best_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                "best_hours": [10, 11, 16, 17, 18],  # 10-11 AM, 4-6 PM
                "avoid_hours": [0, 1, 2, 3, 4, 5, 6, 22, 23]  # Late night/early morning
            }
        }

        # Adjust timing based on content type
        if content_type.lower() == "urgent":
            # For urgent content, expand available hours
            timing_recommendations["email"]["best_hours"] = list(range(8, 20))  # 8 AM - 8 PM
            timing_recommendations["whatsapp"]["best_hours"] = list(range(8, 21))  # 8 AM - 9 PM
        elif content_type.lower() == "promotional":
            # For promotional content, focus on peak engagement times
            timing_recommendations["email"]["best_hours"] = [10, 14, 19]  # 10 AM, 2 PM, 7 PM
            timing_recommendations["whatsapp"]["best_hours"] = [11, 16, 18]  # 11 AM, 4 PM, 6 PM

        # Calculate optimal send time
        now = datetime.now()
        current_hour = now.hour
        current_day = now.strftime("%A")

        optimization_results = {
            "current_time": now.isoformat(),
            "timing_recommendations": timing_recommendations,
            "immediate_send_recommended": False,
            "suggested_delay_hours": 0,
            "total_users": len(users_data)
        }

        # Check if current time is optimal for any channel
        email_optimal = (current_day in timing_recommendations["email"]["best_days"] and
                        current_hour in timing_recommendations["email"]["best_hours"])
        whatsapp_optimal = (current_day in timing_recommendations["whatsapp"]["best_days"] and
                           current_hour in timing_recommendations["whatsapp"]["best_hours"])

        if email_optimal or whatsapp_optimal:
            optimization_results["immediate_send_recommended"] = True
        else:
            # Calculate delay to next optimal time
            next_optimal_hours = []

            # Find next optimal email time
            for hour in timing_recommendations["email"]["best_hours"]:
                if hour > current_hour:
                    next_optimal_hours.append(hour - current_hour)
                    break

            # Find next optimal WhatsApp time
            for hour in timing_recommendations["whatsapp"]["best_hours"]:
                if hour > current_hour:
                    next_optimal_hours.append(hour - current_hour)
                    break

            if next_optimal_hours:
                optimization_results["suggested_delay_hours"] = min(next_optimal_hours)
            else:
                # Next day
                optimization_results["suggested_delay_hours"] = 24 - current_hour + min(
                    timing_recommendations["email"]["best_hours"] +
                    timing_recommendations["whatsapp"]["best_hours"]
                )

        return optimization_results

    def generate_optimization_report(self, channel_results: Dict[str, List[Dict[str, Any]]],
                                   timing_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive optimization report.

        Args:
            channel_results: Results from batch channel selection
            timing_results: Results from timing optimization

        Returns:
            Dictionary containing optimization report
        """
        total_users = sum(len(users) for users in channel_results.values())
        email_users = len(channel_results.get(self.EMAIL, []))
        whatsapp_users = len(channel_results.get(self.WHATSAPP, []))
        unavailable_users = len(channel_results.get("unavailable", []))

        # Calculate average scores
        email_scores = [user.get("email_score", 0) for user in channel_results.get(self.EMAIL, [])]
        whatsapp_scores = [user.get("whatsapp_score", 0) for user in channel_results.get(self.WHATSAPP, [])]

        avg_email_score = sum(email_scores) / len(email_scores) if email_scores else 0
        avg_whatsapp_score = sum(whatsapp_scores) / len(whatsapp_scores) if whatsapp_scores else 0

        report = {
            "summary": {
                "total_users": total_users,
                "email_users": email_users,
                "whatsapp_users": whatsapp_users,
                "unavailable_users": unavailable_users,
                "email_percentage": (email_users / total_users * 100) if total_users > 0 else 0,
                "whatsapp_percentage": (whatsapp_users / total_users * 100) if total_users > 0 else 0
            },
            "channel_scores": {
                "average_email_score": round(avg_email_score, 3),
                "average_whatsapp_score": round(avg_whatsapp_score, 3)
            },
            "timing_optimization": timing_results,
            "recommendations": []
        }

        # Generate recommendations
        if email_users > whatsapp_users:
            report["recommendations"].append("Email is the dominant channel for this campaign")
        elif whatsapp_users > email_users:
            report["recommendations"].append("WhatsApp is the dominant channel for this campaign")
        else:
            report["recommendations"].append("Balanced distribution between email and WhatsApp")

        if unavailable_users > 0:
            report["recommendations"].append(f"{unavailable_users} users have no valid contact information")

        if not timing_results.get("immediate_send_recommended", False):
            delay_hours = timing_results.get("suggested_delay_hours", 0)
            report["recommendations"].append(f"Consider delaying send by {delay_hours} hours for optimal timing")

        return report


# API Functions for external use

def select_optimal_channel(user_data: Dict[str, Any], content_type: str = "general",
                          urgency_level: str = "medium", performance_data_path: str = None,
                          weights: Dict[str, float] = None) -> Dict[str, Any]:
    """
    Select the optimal channel for a single user.

    Args:
        user_data: Dictionary containing user data
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with selected channel and scores
    """
    optimizer = ChannelOptimizer(weights=weights)

    # Load engagement data if performance file provided
    engagement_data = None
    if performance_data_path and os.path.exists(performance_data_path):
        engagement_data = optimizer._calculate_engagement_from_performance_data(performance_data_path)

    # Select channel for user
    channel, scores = optimizer.select_channel(
        user_data,
        content_type=content_type,
        urgency_level=urgency_level,
        engagement_data=engagement_data
    )

    return {
        "selected_channel": channel,
        "email_score": scores.get("email", 0),
        "whatsapp_score": scores.get("whatsapp", 0),
        "user_email": user_data.get("user_email", ""),
        "phone_number": user_data.get("phone_number", "")
    }


def optimize_campaign_channels(users_data_path: str, content_type: str = "general",
                              urgency_level: str = "medium", performance_data_path: str = None,
                              output_dir: str = None, weights: Dict[str, float] = None) -> Dict[str, Any]:
    """
    Optimize channels for a campaign and generate segmented user lists.

    Args:
        users_data_path: Path to the CSV file with user data
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        output_dir: Directory to save output CSV files (default: current directory)
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with optimization results and file paths
    """
    try:
        # Load user data
        if not os.path.exists(users_data_path):
            raise FileNotFoundError(f"User data file not found: {users_data_path}")

        users_df = pd.read_csv(users_data_path)
        users_data = users_df.to_dict('records')

        # Initialize optimizer
        optimizer = ChannelOptimizer(weights=weights)

        # Load engagement data if provided
        engagement_data = None
        if performance_data_path and os.path.exists(performance_data_path):
            engagement_data = optimizer._calculate_engagement_from_performance_data(performance_data_path)

        # Process channels
        channel_results = optimizer.batch_select_channels(
            users_data,
            content_type=content_type,
            urgency_level=urgency_level,
            engagement_data=engagement_data
        )

        # Optimize timing
        timing_results = optimizer.optimize_campaign_timing(users_data, content_type)

        # Generate report
        optimization_report = optimizer.generate_optimization_report(channel_results, timing_results)

        # Save segmented CSV files
        output_files = {}
        if output_dir is None:
            output_dir = os.path.dirname(users_data_path) or "."

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for channel, users in channel_results.items():
            if users:  # Only save if there are users for this channel
                df = pd.DataFrame(users)
                filename = f"campaign_{channel}_users_{timestamp}.csv"
                filepath = os.path.join(output_dir, filename)
                df.to_csv(filepath, index=False)
                output_files[channel] = filepath

        return {
            "success": True,
            "optimization_report": optimization_report,
            "channel_results": channel_results,
            "timing_results": timing_results,
            "output_files": output_files,
            "timestamp": timestamp
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def get_channel_recommendations(user_data: Dict[str, Any], content_type: str = "general") -> Dict[str, Any]:
    """
    Get channel recommendations with explanations for a user.

    Args:
        user_data: Dictionary containing user data
        content_type: Type of content being sent

    Returns:
        Dictionary with recommendations and explanations
    """
    optimizer = ChannelOptimizer()

    # Get all factor scores
    availability = optimizer.get_channel_availability(user_data)
    engagement = optimizer.get_engagement_scores(user_data)
    content_suitability = optimizer.get_content_suitability(content_type)
    urgency = optimizer.get_urgency_scores("medium")  # Default urgency

    # Select optimal channel
    selected_channel, scores = optimizer.select_channel(user_data, content_type)

    # Generate explanations
    explanations = []

    if availability["email"] == 0:
        explanations.append("Email not available - no valid email address")
    elif availability["email"] == 1:
        explanations.append("Email available")

    if availability["whatsapp"] == 0:
        explanations.append("WhatsApp not available - no valid phone number")
    elif availability["whatsapp"] == 1:
        explanations.append("WhatsApp available")

    if engagement["email"] > 0.7:
        explanations.append("High email engagement expected")
    elif engagement["email"] < 0.3:
        explanations.append("Low email engagement expected")

    if content_suitability["email"] > content_suitability["whatsapp"]:
        explanations.append(f"Content type '{content_type}' better suited for email")
    elif content_suitability["whatsapp"] > content_suitability["email"]:
        explanations.append(f"Content type '{content_type}' better suited for WhatsApp")

    return {
        "recommended_channel": selected_channel,
        "channel_scores": scores,
        "factor_scores": {
            "availability": availability,
            "engagement": engagement,
            "content_suitability": content_suitability,
            "urgency": urgency
        },
        "explanations": explanations,
        "confidence": max(scores.values()) if scores else 0
    }


if __name__ == "__main__":
    # Example usage
    sample_user = {
        "user_email": "<EMAIL>",
        "phone_number": "+1234567890",
        "user_behaviour": "Interested in AI courses, frequently opens emails"
    }

    # Test single user optimization
    result = select_optimal_channel(
        user_data=sample_user,
        content_type="promotional",
        urgency_level="medium"
    )

    print("Channel Optimization Result:")
    print(f"Selected Channel: {result['selected_channel']}")
    print(f"Email Score: {result['email_score']:.3f}")
    print(f"WhatsApp Score: {result['whatsapp_score']:.3f}")

    # Test recommendations
    recommendations = get_channel_recommendations(sample_user, "promotional")
    print(f"\nRecommendations: {recommendations['recommended_channel']}")
    print(f"Confidence: {recommendations['confidence']:.3f}")
    print("Explanations:")
    for explanation in recommendations['explanations']:
        print(f"- {explanation}")
