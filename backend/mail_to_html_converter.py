"""
Mail to HTML Converter Backend Script
Aggregates all mail-to-html conversion functionality including text to HTML conversion,
brand styling, CTA integration, and template processing.

This script combines functionality from:
- src/openengage/core/email_formatter.py
- backend/html_email_generator.py
- Related utility functions

Inputs:
- email_content: Dict with 'subject', 'content', 'preheader' keys
- product_url: Optional product URL for CTA linking
- product_name: Optional product name for link text
- communication_settings: Dict with UTM parameters and sender info
- recipient_email: Optional recipient email for unsubscribe link
- recipient_first_name: Optional recipient first name for personalization
- brand_guidelines: Optional dict with brand styling guidelines
- template_name: Optional template name for CTA lookup
- organization_url: Optional organization URL for brand guidelines

Outputs:
- HTML formatted email content with embedded styling and CTA buttons
"""

import re
import html
import logging
import json
import os
import base64
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from datetime import datetime
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

class MailToHTMLConverter:
    """Converter for transforming plain text emails to HTML with brand styling"""

    def __init__(self):
        """Initialize the converter"""
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the converter"""
        logger = logging.getLogger("openengage.mail_to_html_converter")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def encrypt_email(self, email: str) -> str:
        """
        Encrypt an email address using AES encryption for unsubscribe link generation.
        
        Args:
            email: The email address to encrypt
            
        Returns:
            Encrypted email string safe for URL usage
        """
        try:
            key = "CQv9UQ/cqt8XNhCiq6JmZQ=="
            key = base64.b64decode(key)
            cipher = AES.new(key, AES.MODE_CBC)
            iv = cipher.iv
            ciphertext = cipher.encrypt(pad(email.encode(), AES.block_size))
            encrypted_email = base64.urlsafe_b64encode(iv + ciphertext).decode('utf-8')
            return encrypted_email
        except Exception as e:
            self.logger.warning(f"Failed to encrypt email for unsubscribe: {str(e)}")
            return ""

    def create_unsubscribe_link(self, email: str) -> str:
        """
        Create a secure unsubscribe link for the given email address.
        
        Args:
            email: The email address to create an unsubscribe link for
            
        Returns:
            Complete unsubscribe URL
        """
        if not email:
            return "#"
        
        try:
            encrypted_email = self.encrypt_email(email)
            if encrypted_email:
                url = f'https://www.analyticsvidhya.com/one-tap-unsubscribe/?email_hash={encrypted_email}'
                url = url + "&utm_source=email_openengage"
                return url
            else:
                return f"#unsubscribe-{email}"
        except Exception as e:
            self.logger.warning(f"Failed to create unsubscribe link: {str(e)}")
            return f"#unsubscribe-{email}"

    def load_brand_guidelines(self, organization_url: str = None) -> Dict[str, Any]:
        """
        Load brand guidelines from file.

        Args:
            organization_url: The organization URL to filter by

        Returns:
            Dict containing brand guidelines or default guidelines
        """
        guidelines_path = os.path.join('data', 'brand_guidelines.json')

        # Default brand guidelines
        default_guidelines = {
            "primary_color": "#2674ED",
            "secondary_color": "#F9C823",
            "accent_color": "#FF6B6B",
            "neutral_color": "#6C757D",
            "background_color": "#FFFFFF",
            "text_color": "#333333",
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "4px",
            "font": "Arial",
            "font_size": "16px",
            "font_weight": "Normal",
            "organization_url": organization_url or ""
        }

        try:
            if os.path.exists(guidelines_path):
                with open(guidelines_path, 'r', encoding='utf-8') as f:
                    all_guidelines = json.load(f)

                if organization_url and isinstance(all_guidelines, dict):
                    # Clean up the organization URL
                    clean_org_url = organization_url.rstrip('/').lower()
                    if clean_org_url.startswith('www.'):
                        clean_org_url = clean_org_url[4:]
                    elif clean_org_url.startswith('http://www.'):
                        clean_org_url = clean_org_url[11:]
                    elif clean_org_url.startswith('https://www.'):
                        clean_org_url = clean_org_url[12:]
                    elif clean_org_url.startswith('http://'):
                        clean_org_url = clean_org_url[7:]
                    elif clean_org_url.startswith('https://'):
                        clean_org_url = clean_org_url[8:]

                    # Try exact match first
                    if organization_url in all_guidelines:
                        return all_guidelines[organization_url]

                    # Try with cleaned URL
                    for url, guidelines in all_guidelines.items():
                        clean_stored_url = url.rstrip('/').lower()
                        if clean_stored_url.startswith('www.'):
                            clean_stored_url = clean_stored_url[4:]
                        elif clean_stored_url.startswith('http://www.'):
                            clean_stored_url = clean_stored_url[11:]
                        elif clean_stored_url.startswith('https://www.'):
                            clean_stored_url = clean_stored_url[12:]
                        elif clean_stored_url.startswith('http://'):
                            clean_stored_url = clean_stored_url[7:]
                        elif clean_stored_url.startswith('https://'):
                            clean_stored_url = clean_stored_url[8:]

                        if clean_org_url == clean_stored_url:
                            return guidelines

                # Return first organization's guidelines or default
                if isinstance(all_guidelines, dict):
                    if 'default_organization' in all_guidelines:
                        return all_guidelines['default_organization']
                    elif len(all_guidelines) > 0:
                        return next(iter(all_guidelines.values()))

        except Exception as e:
            self.logger.warning(f"Error loading brand guidelines: {str(e)}")

        return default_guidelines

    def load_template_cta(self, template_name: str) -> Optional[str]:
        """
        Load CTA text for a specific template from the JSON file.

        Args:
            template_name: Name of the template to get CTA for

        Returns:
            CTA text if found, None otherwise to use default CTA
        """
        if not template_name:
            return None

        try:
            cta_file_path = 'data/templates/template_ctas.json'

            if not os.path.exists(cta_file_path):
                return None

            with open(cta_file_path, 'r', encoding='utf-8') as f:
                ctas = json.load(f)

            cta_text = ctas.get(template_name)

            if cta_text:
                cta_text = cta_text.strip().strip('"').strip("'")
                return cta_text
            else:
                return None

        except Exception as e:
            self.logger.warning(f"Error loading CTA for template '{template_name}': {str(e)}")
            return None

    def get_cta_text_for_template(self, template_name: str, default_cta: str = 'Learn More') -> str:
        """
        Get CTA text for a template with fallback to default.

        Args:
            template_name: Name of the template to get CTA for
            default_cta: Default CTA text to use if template not found

        Returns:
            CTA text (either custom from JSON or default)
        """
        if not template_name:
            return default_cta

        # Try exact match first
        custom_cta = self.load_template_cta(template_name)
        if custom_cta:
            return custom_cta

        # If exact match fails, try pattern matching
        try:
            cta_file_path = 'data/templates/template_ctas.json'
            if os.path.exists(cta_file_path):
                with open(cta_file_path, 'r', encoding='utf-8') as f:
                    ctas = json.load(f)

                # Extract key parts from template_name for pattern matching
                template_parts = template_name.lower().replace('_', ' ').split()

                # Look for templates that contain similar patterns
                best_match = None
                max_matches = 0

                for cta_template_name, cta_text in ctas.items():
                    cta_parts = cta_template_name.lower().replace('_', ' ').split()

                    # Count matching parts
                    matches = sum(1 for part in template_parts if any(part in cta_part for cta_part in cta_parts))

                    if matches > max_matches and matches >= 2:  # Require at least 2 matching parts
                        max_matches = matches
                        best_match = cta_text

                if best_match:
                    best_match = best_match.strip().strip('"').strip("'")
                    return best_match

        except Exception as e:
            self.logger.warning(f"Error in pattern matching for template '{template_name}': {str(e)}")

        return default_cta

    def add_utm_parameters(self, url: str, communication_settings: Dict[str, Any] = None) -> str:
        """
        Add UTM parameters to a URL based on communication settings.

        Args:
            url: The URL to add UTM parameters to
            communication_settings: Dictionary containing UTM parameters

        Returns:
            URL with UTM parameters added
        """
        if not url or not communication_settings:
            return url

        try:
            # Parse the URL
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            # Add UTM parameters from communication settings
            utm_params = {
                'utm_source': communication_settings.get('utm_source', 'email'),
                'utm_medium': communication_settings.get('utm_medium', 'email'),
                'utm_campaign': communication_settings.get('utm_campaign', 'campaign'),
                'utm_content': communication_settings.get('utm_content', 'content')
            }

            # Add UTM parameters to query params
            for key, value in utm_params.items():
                if value:  # Only add if value is not empty
                    query_params[key] = [value]

            # Reconstruct the URL
            new_query = urlencode(query_params, doseq=True)
            new_url = urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                new_query,
                parsed_url.fragment
            ))

            return new_url

        except Exception as e:
            self.logger.warning(f"Error adding UTM parameters to URL: {str(e)}")
            return url

    def process_inline_formatting(self, text: str) -> str:
        """
        Process inline text formatting like bold, italic, and links.

        Args:
            text: Plain text content

        Returns:
            HTML formatted text with inline styling
        """
        if not text:
            return text

        # Process bold text (**text** or __text__)
        text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
        text = re.sub(r'__(.*?)__', r'<strong>\1</strong>', text)

        # Process italic text (*text* or _text_)
        text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
        text = re.sub(r'_(.*?)_', r'<em>\1</em>', text)

        # Process URLs (but not if they're already in HTML tags)
        url_pattern = r'(?<!href=")(?<!href=\')(?<!src=")(?<!src=\')https?://[^\s<>"\']+(?![^<]*>)'
        text = re.sub(url_pattern, r'<a href="\g<0>" style="color: #2674ED; text-decoration: none;">\g<0></a>', text)

        return text

    def create_cta_button(self, cta_text: str, url: str, brand_guidelines: Dict[str, Any] = None) -> str:
        """
        Create an HTML CTA button using brand guidelines.

        Args:
            cta_text: Text for the CTA button
            url: URL for the button
            brand_guidelines: Dictionary containing brand guidelines

        Returns:
            HTML for a styled CTA button
        """
        # Default values
        bg_color = "#2674ED"
        text_color = "#FFFFFF"
        border_radius = "4px"
        padding = "12px 24px"
        font_family = "Arial, sans-serif"
        font_size = "16px"
        font_weight = "bold"

        # Apply brand guidelines if available
        if brand_guidelines:
            # Get button color based on the selected type
            button_color_type = brand_guidelines.get("button_color_type", "Primary Color")

            if button_color_type == "Primary Color":
                bg_color = brand_guidelines.get("primary_color", bg_color)
            elif button_color_type == "Secondary Color":
                bg_color = brand_guidelines.get("secondary_color", bg_color)
            elif button_color_type == "Accent Color":
                bg_color = brand_guidelines.get("accent_color", bg_color)

            # Get border radius from button style
            button_style = brand_guidelines.get("button_style", "Rounded")
            if button_style == "Square":
                border_radius = "0px"
            elif button_style == "Rounded":
                border_radius = "4px"
            elif button_style == "Pill":
                border_radius = "25px"

            # Get font settings
            font_family = brand_guidelines.get("font", "Arial") + ", sans-serif"
            font_size = brand_guidelines.get("font_size", "16px")
            font_weight = brand_guidelines.get("font_weight", "bold").lower()

        # Create the button HTML
        button_html = f'''
        <div style="text-align: center; margin: 20px 0;">
            <a href="{url}" style="
                display: inline-block;
                background-color: {bg_color};
                color: {text_color};
                padding: {padding};
                text-decoration: none;
                border-radius: {border_radius};
                font-family: {font_family};
                font-size: {font_size};
                font-weight: {font_weight};
                border: none;
                cursor: pointer;
                text-align: center;
                line-height: 1.2;
            ">{cta_text}</a>
        </div>
        '''

        return button_html

    def process_paragraphs(self, content: str, sender_name: str = None, communication_settings: Dict[str, Any] = None,
                          product_name: str = None, product_url: str = None, recipient_first_name: str = None,
                          brand_guidelines: Dict[str, Any] = None) -> str:
        """
        Process text content into HTML paragraphs.

        Args:
            content: Plain text content
            sender_name: Name of the sender for salutation replacement
            communication_settings: Dictionary containing UTM parameters
            product_name: Name of the product for link text
            product_url: URL of the product to link the product name
            recipient_first_name: First name of the recipient for personalized greeting
            brand_guidelines: Dictionary containing brand guidelines

        Returns:
            HTML formatted paragraphs
        """
        if not content:
            return ""

        # Check if content starts with a greeting
        greeting_match = re.match(r'^\s*(Hi|Hello|Dear|Hey)\s*(?:[^,\n]*)?([,.])?\s*', content, re.IGNORECASE)

        if greeting_match and recipient_first_name:
            # Replace generic greeting with personalized one
            greeting_end = greeting_match.end()
            personalized_greeting = f"Hi {recipient_first_name},"
            content = personalized_greeting + content[greeting_end:]

        # Split content into paragraphs
        paragraphs = content.split('\n\n')
        html_paragraphs = []

        for para in paragraphs:
            para = para.strip()
            if not para:
                continue

            # Handle line breaks within paragraphs
            para = para.replace('\n', '<br>')

            # Replace product name with linked version if product_url is provided
            if product_name and product_url:
                # Add UTM parameters to product URL
                linked_url = self.add_utm_parameters(product_url, communication_settings)

                # Create a styled link for the product name
                product_link = f'<a href="{linked_url}" style="color: #2674ED; text-decoration: none; font-weight: bold;">{product_name}</a>'

                # Replace product name with link (case-insensitive)
                para = re.sub(re.escape(product_name), product_link, para, flags=re.IGNORECASE)

            # Replace sender name placeholders
            if sender_name:
                para = para.replace('[Sender Name]', sender_name)
                para = para.replace('[Company Name]', sender_name)

            # Process inline formatting
            formatted_para = self.process_inline_formatting(para)

            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{formatted_para}</p>')

        return '\n'.join(html_paragraphs)

    def create_html_email_template(self, body_content: str, company_name: str = "OpenEngage",
                                  product_url: str = None, recipient_email: str = None,
                                  brand_guidelines: Dict[str, Any] = None, template_name: str = None) -> str:
        """
        Create a complete HTML email template using brand guidelines.

        Args:
            body_content: HTML formatted email body content
            company_name: Name of the company sending the email
            product_url: URL of the product to link the CTA button
            recipient_email: Email address of the recipient for unsubscribe link generation
            brand_guidelines: Dictionary containing brand guidelines for styling
            template_name: Name of the template for CTA lookup

        Returns:
            Complete HTML email with embedded CTA button and styling
        """
        # Process CTA placement
        if product_url:
            # Get CTA text using the centralized helper function
            cta_text = self.get_cta_text_for_template(template_name, 'Learn More')

            # Create the CTA button with the determined text
            cta_button = self.create_cta_button(cta_text, product_url, brand_guidelines)

            # Try to insert after the third paragraph
            paragraphs = list(re.split(r'</p>\s*<p[^>]*>', body_content))

            if len(paragraphs) >= 3:
                # Insert CTA after third paragraph
                paragraphs.insert(3, cta_button)
                body_content = '</p><p style="margin: 10px 0; line-height: 1.5;">'.join(paragraphs)
            else:
                # If less than 3 paragraphs, append CTA at the end
                body_content += cta_button

        # Create unsubscribe link
        unsubscribe_link = ""
        if recipient_email:
            unsubscribe_url = self.create_unsubscribe_link(recipient_email)
            unsubscribe_link = f'<a href="{unsubscribe_url}" style="color: #666; text-decoration: underline;">Unsubscribe</a>'

        # Get brand colors
        primary_color = "#2674ED"
        background_color = "#FFFFFF"
        text_color = "#333333"

        if brand_guidelines:
            primary_color = brand_guidelines.get("primary_color", primary_color)
            background_color = brand_guidelines.get("background_color", background_color)
            text_color = brand_guidelines.get("text_color", text_color)

        # Create the complete HTML email
        html_email = f'''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Email from {company_name}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: {text_color};
                    background-color: {background_color};
                    margin: 0;
                    padding: 0;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: {background_color};
                    padding: 20px;
                }}
                .email-body {{
                    background-color: {background_color};
                    padding: 20px;
                    border-radius: 8px;
                }}
                .email-footer {{
                    text-align: center;
                    padding: 20px;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #eee;
                    margin-top: 20px;
                }}
                a {{
                    color: {primary_color};
                    text-decoration: none;
                }}
                a:hover {{
                    text-decoration: underline;
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-body">
                    {body_content}
                </div>
                <div class="email-footer">
                    <p>© 2024 {company_name}. All rights reserved.</p>
                    {f"<p>{unsubscribe_link}</p>" if unsubscribe_link else ""}
                </div>
            </div>
        </body>
        </html>
        '''

        return html_email

    def text_to_html(self, email_content: Dict[str, Any], product_url: Optional[str] = None,
                     product_name: Optional[str] = None, communication_settings: Optional[Dict[str, Any]] = None,
                     recipient_email: Optional[str] = None, recipient_first_name: Optional[str] = None,
                     brand_guidelines: Optional[Dict[str, Any]] = None, template_name: Optional[str] = None,
                     organization_url: Optional[str] = None) -> str:
        """
        Convert plain text email content to HTML format with brand styling and CTA integration.

        Args:
            email_content: Dictionary containing email content with 'subject' and 'content' keys
            product_url: URL of the product for linking (optional)
            product_name: Name of the product for link text (optional)
            communication_settings: Dictionary containing UTM parameters and sender info (optional)
            recipient_email: Email address of the recipient for unsubscribe link (optional)
            recipient_first_name: First name of the recipient for personalized greeting (optional)
            brand_guidelines: Dictionary containing brand guidelines for styling (optional)
            template_name: Name of the template for CTA lookup (optional)
            organization_url: Organization URL for brand guidelines lookup (optional)

        Returns:
            HTML formatted email content with embedded styling and CTA buttons
        """
        if not email_content or not isinstance(email_content, dict):
            return ""

        # Extract content
        content = email_content.get('content', '')

        if not content:
            return ""

        # Remove any "Body:" or "Email Body:" prefixes
        content = re.sub(r'^\s*(?:Body|Email Body)\s*:\s*', '', content)

        # Check if content already contains HTML tags
        contains_html = re.search(r'<[a-z]+[^>]*>', content, re.IGNORECASE) is not None

        # Only escape HTML special characters if the content doesn't already contain HTML
        if not contains_html:
            content = html.escape(content)

        # Extract sender name from communication settings
        sender_name = "OpenEngage Team"
        if communication_settings and 'sender_name' in communication_settings:
            sender_name = communication_settings.get('sender_name')

        # Extract organization URL from communication settings if not provided
        if not organization_url and communication_settings:
            organization_url = communication_settings.get('organization_url')

        # Extract recipient's first name from email if not provided
        if recipient_first_name is None and recipient_email:
            try:
                local_part = recipient_email.split('@')[0]
                if '.' in local_part:
                    first_name = local_part.split('.')[0]
                else:
                    match = re.search(r'^([a-zA-Z]+)', local_part)
                    if match:
                        first_name = match.group(1)
                    else:
                        first_name = local_part

                recipient_first_name = first_name.capitalize()
            except Exception:
                pass

        # Use provided brand guidelines or load from file
        if brand_guidelines is None:
            brand_guidelines = self.load_brand_guidelines(organization_url)

        # Process the content
        paragraphs = self.process_paragraphs(content, sender_name, communication_settings,
                                           product_name, product_url, recipient_first_name, brand_guidelines)

        # Get company name for footer
        company_name = "OpenEngage"
        if communication_settings and 'sender_name' in communication_settings:
            company_name = communication_settings.get('sender_name')

        # Create the full HTML email
        html_email = self.create_html_email_template(paragraphs, company_name, product_url,
                                                   recipient_email, brand_guidelines, template_name)

        return html_email


# API Functions for external use

def convert_text_to_html(email_content: Dict[str, Any], product_url: str = None,
                        product_name: str = None, communication_settings: Dict[str, Any] = None,
                        recipient_email: str = None, recipient_first_name: str = None,
                        brand_guidelines: Dict[str, Any] = None, template_name: str = None,
                        organization_url: str = None) -> str:
    """
    Convert plain text email content to HTML format with brand styling.

    Args:
        email_content: Dictionary containing email content
        product_url: Optional product URL for CTA linking
        product_name: Optional product name for link text
        communication_settings: Optional dict with UTM parameters and sender info
        recipient_email: Optional recipient email for unsubscribe link
        recipient_first_name: Optional recipient first name for personalization
        brand_guidelines: Optional dict with brand styling guidelines
        template_name: Optional template name for CTA lookup
        organization_url: Optional organization URL for brand guidelines

    Returns:
        HTML formatted email content
    """
    converter = MailToHTMLConverter()
    return converter.text_to_html(
        email_content=email_content,
        product_url=product_url,
        product_name=product_name,
        communication_settings=communication_settings,
        recipient_email=recipient_email,
        recipient_first_name=recipient_first_name,
        brand_guidelines=brand_guidelines,
        template_name=template_name,
        organization_url=organization_url
    )


def load_brand_guidelines_for_organization(organization_url: str = None) -> Dict[str, Any]:
    """
    Load brand guidelines for a specific organization.

    Args:
        organization_url: Organization URL

    Returns:
        Dictionary containing brand guidelines
    """
    converter = MailToHTMLConverter()
    return converter.load_brand_guidelines(organization_url)


def get_template_cta_text(template_name: str, default_cta: str = 'Learn More') -> str:
    """
    Get CTA text for a specific template.

    Args:
        template_name: Name of the template
        default_cta: Default CTA text if template not found

    Returns:
        CTA text for the template
    """
    converter = MailToHTMLConverter()
    return converter.get_cta_text_for_template(template_name, default_cta)


if __name__ == "__main__":
    # Example usage
    sample_email = {
        "subject": "Welcome to Our Platform",
        "content": "Hi there,\n\nWelcome to our amazing platform! We're excited to have you on board.\n\nBest regards,\nThe Team"
    }

    sample_settings = {
        "sender_name": "OpenEngage Team",
        "utm_source": "email",
        "utm_medium": "email",
        "utm_campaign": "welcome"
    }

    html_result = convert_text_to_html(
        email_content=sample_email,
        product_url="https://example.com",
        product_name="Our Platform",
        communication_settings=sample_settings,
        recipient_email="<EMAIL>",
        template_name="welcome"
    )

    print("HTML Email Generated Successfully!")
    print(f"Length: {len(html_result)} characters")
